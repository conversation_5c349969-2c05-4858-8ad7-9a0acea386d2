@extends('layouts.app')

@section('title', 'Live Map - Delivery Tracking')

@push('styles')
<style>
    /* Ensure the map container has a defined size */
    #map {
        height: 100%;
        width: 100%;
        min-height: 600px; /* Ensure a minimum height */
    }
    /* Custom InfoWindow styling */
    .gm-style-iw {
        max-width: 300px;
        padding: 0 !important;
    }
    .gm-style-iw-c {
        border-radius: 8px !important;
        padding: 0 !important;
    }
    .gm-style-iw-d {
        overflow: hidden !important;
    }
</style>
@endpush

@section('content')
<div class="min-h-screen bg-gray-50">
    
    <!-- Header -->
    <div class="bg-white shadow-sm">
        <div class="container mx-auto px-6 py-6">
            <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Live Delivery Map</h1>
                    <p class="text-gray-600 mt-1">Real-time tracking of all active deliveries</p>
                </div>
                
                <!-- Controls -->
                <div class="mt-4 lg:mt-0 flex flex-wrap gap-4">
                    <!-- Search -->
                    <div class="relative">
                        <input type="text" id="searchInput" placeholder="Search bookings, addresses..."
                               class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                    </div>
                    
                    <!-- Status Filter -->
                    <select id="statusFilter" class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                        <option value="active">Active Deliveries</option>
                        <option value="all">All Deliveries</option>
                        <option value="pending">Pending Assignment</option>
                        <option value="confirmed">Confirmed</option>
                        <option value="delivery_enroute">Delivery Enroute</option>
                        <option value="in_progress">In Progress</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                    
                    <!-- Refresh Button -->
                    <button onclick="refreshMap()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-sync-alt mr-2"></i>Refresh
                    </button>
                    
                    <!-- Auto-refresh Toggle -->
                    <label class="flex items-center">
                        <input type="checkbox" id="autoRefresh" checked class="mr-2 h-4 w-4 text-orange-600 rounded focus:ring-orange-500">
                        <span class="text-sm text-gray-700">Auto-refresh</span>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-6 py-6">
        
        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white rounded-xl shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-orange-100">
                        <i class="fas fa-truck text-orange-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Active Deliveries</p>
                        <p class="text-2xl font-bold text-gray-900" id="activeCount">{{ $stats['total_active'] }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100">
                        <i class="fas fa-clock text-yellow-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Pending Assignment</p>
                        <p class="text-2xl font-bold text-gray-900" id="pendingCount">{{ $stats['pending_assignment'] }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100">
                        <i class="fas fa-route text-purple-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">In Transit</p>
                        <p class="text-2xl font-bold text-gray-900" id="transitCount">{{ $stats['in_transit'] }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Completed Today</p>
                        <p class="text-2xl font-bold text-gray-900" id="completedCount">{{ $stats['completed_today'] }}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            
            <!-- Map Container -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-bold text-gray-900">Live Delivery Map</h3>
                            <!-- Map status indicator is now part of the map itself -->
                        </div>
                    </div>
                    <div class="relative">
                        <!-- The map renders here immediately. The gray background acts as a placeholder. -->
                        <div id="map" class="bg-gray-100"></div>

                        <!-- Map Status Indicator -->
                        <div id="map-status" class="absolute top-4 left-4 bg-white/80 backdrop-blur-sm px-3 py-1 rounded-full shadow-md text-xs font-medium text-gray-700 z-10 transition-all">
                            <i class="fas fa-spinner fa-spin mr-2"></i>Initializing Map...
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Delivery List -->
            <div class="bg-white rounded-xl shadow-sm">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-bold text-gray-900">Active Deliveries</h3>
                    <p class="text-sm text-gray-600" id="deliveryCount">{{ count($bookings) }} deliveries found</p>
                </div>
                <div class="max-h-[550px] overflow-y-auto" id="deliveryList">
                    @forelse($bookings as $booking)
                        <div class="p-4 border-b border-gray-100 last:border-b-0 hover:bg-orange-50 cursor-pointer delivery-item transition-colors" 
                             data-booking-id="{{ $booking->id }}"
                             onclick="focusOnDelivery({{ $booking->id }})">
                            <div class="flex items-start justify-between">
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center mb-2">
                                        <span class="text-sm font-bold text-gray-900">{{ $booking->booking_id }}</span>
                                        <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium
                                            @switch($booking->status)
                                                @case('confirmed') bg-blue-100 text-blue-800 @break
                                                @case('in_progress') bg-purple-100 text-purple-800 @break
                                                @case('delivery_enroute') bg-orange-100 text-orange-800 @break
                                                @case('completed') bg-green-100 text-green-800 @break
                                                @case('cancelled') bg-red-100 text-red-800 @break
                                                @default bg-gray-100 text-gray-800
                                            @endswitch
                                        ">
                                            {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                                        </span>
                                    </div>
                                    <div class="space-y-1">
                                        <div class="flex items-center text-xs text-gray-600">
                                            <i class="fas fa-map-marker-alt text-green-500 w-4 text-center mr-2"></i>
                                            <span class="truncate">{{ $booking->pickup_address }}</span>
                                        </div>
                                        <div class="flex items-center text-xs text-gray-600">
                                            <i class="fas fa-flag-checkered text-red-500 w-4 text-center mr-2"></i>
                                            <span class="truncate">{{ $booking->delivery_address }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="ml-2 flex-shrink-0">
                                    <button onclick="event.stopPropagation(); viewBookingDetails({{ $booking->id }})" 
                                            class="text-gray-400 hover:text-orange-600 transition-colors p-2 rounded-full hover:bg-orange-100">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="p-6 text-center text-gray-500">
                            <i class="fas fa-box-open text-3xl mb-2 text-gray-400"></i>
                            <p>No active deliveries found.</p>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Booking Details Modal -->
<div id="bookingModal" class="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center hidden z-50 transition-opacity">
    <div class="relative mx-auto p-6 border bg-white w-11/12 max-w-2xl shadow-lg rounded-xl transform transition-all" id="modal-panel">
        <div class="flex justify-between items-center pb-3 border-b">
            <h3 class="text-xl font-bold text-gray-900" id="modalTitle">Booking Details</h3>
            <button onclick="closeBookingModal()" class="text-gray-400 hover:text-gray-600 p-2 rounded-full hover:bg-gray-100">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div id="modalContent" class="mt-4">
            <!-- Content will be loaded here -->
            <div class="text-center py-8"><i class="fas fa-spinner fa-spin text-3xl text-orange-500"></i></div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://maps.googleapis.com/maps/api/js?key={{ config('services.google_maps.api_key') }}&libraries=places&callback=initMap" async defer></script>

<script>
// --- GLOBAL MAP VARIABLES ---
let map;
let directionsService;
let activeInfoWindows = [];
let mapElements = {
    routes: [], // Will store { bookingId, renderer, status, pickup, delivery }
    markers: [] // Will store all markers
};
let autoRefreshTimer;

// --- MAP INITIALIZATION ---

/**
 * This function is called by the Google Maps API script once it's loaded (due to `&callback=initMap`).
 * This is the entry point for all map functionality.
 */
window.initMap = function() {
    console.log("🗺️ Google Maps API loaded. Initializing map...");

    const defaultCenter = { lat: 5.6037, lng: -0.1870 }; // Default to Accra, Ghana

    map = new google.maps.Map(document.getElementById('map'), {
        center: defaultCenter,
        zoom: 12,
        mapId: 'TTAJET_DELIVERY_MAP', // Using a Map ID for advanced styling
        styles: [ // A cleaner, modern map style
            { elementType: "geometry", stylers: [{ color: "#f5f5f5" }] },
            { elementType: "labels.icon", stylers: [{ visibility: "off" }] },
            { elementType: "labels.text.fill", stylers: [{ color: "#616161" }] },
            { elementType: "labels.text.stroke", stylers: [{ color: "#f5f5f5" }] },
            { featureType: "administrative.land_parcel", elementType: "labels.text.fill", stylers: [{ color: "#bdbdbd" }] },
            { featureType: "poi", elementType: "geometry", stylers: [{ color: "#eeeeee" }] },
            { featureType: "poi", elementType: "labels.text.fill", stylers: [{ color: "#757575" }] },
            { featureType: "poi.park", elementType: "geometry", stylers: [{ color: "#e5e5e5" }] },
            { featureType: "poi.park", elementType: "labels.text.fill", stylers: [{ color: "#9e9e9e" }] },
            { featureType: "road", elementType: "geometry", stylers: [{ color: "#ffffff" }] },
            { featureType: "road.arterial", elementType: "labels.text.fill", stylers: [{ color: "#757575" }] },
            { featureType: "road.highway", elementType: "geometry", stylers: [{ color: "#dadada" }] },
            { featureType: "road.highway", elementType: "labels.text.fill", stylers: [{ color: "#616161" }] },
            { featureType: "road.local", elementType: "labels.text.fill", stylers: [{ color: "#9e9e9e" }] },
            { featureType: "transit.line", elementType: "geometry", stylers: [{ color: "#e5e5e5" }] },
            { featureType: "transit.station", elementType: "geometry", stylers: [{ color: "#eeeeee" }] },
            { featureType: "water", elementType: "geometry", stylers: [{ color: "#c9c9c9" }] },
            { featureType: "water", elementType: "labels.text.fill", stylers: [{ color: "#9e9e9e" }] },
        ],
        mapTypeControl: false,
        streetViewControl: false,
        fullscreenControl: true,
        zoomControlOptions: {
            position: google.maps.ControlPosition.RIGHT_BOTTOM,
        },
    });

    directionsService = new google.maps.DirectionsService();

    // Once the map is initialized, fetch data and set up UI
    fetchAndRenderData();
    setupEventListeners();
    startAutoRefresh();
};

// --- DATA FETCHING & RENDERING ---

/**
 * Fetches all delivery data and orchestrates the rendering process.
 */
async function fetchAndRenderData() {
    updateMapStatus('loading', 'Fetching delivery data...');
    try {
        const response = await fetch("{{ route('api.map.deliveries') }}", {
             headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json'
            }
        });
        if (!response.ok) throw new Error(`Network response was not ok: ${response.statusText}`);
        
        const deliveries = await response.json();
        
        clearMapElements();
        
        if (deliveries.length === 0) {
            updateMapStatus('ready', 'No active deliveries to display.');
            return;
        }

        // Render all routes and markers concurrently
        await Promise.all(deliveries.map(delivery => renderSingleRoute(delivery)));
        
        fitMapToAllRoutes();
        updateMapStatus('ready', `${deliveries.length} routes displayed.`);

    } catch (error) {
        console.error('❌ Error fetching delivery data:', error);
        updateMapStatus('error', 'Could not load map data.');
    }
}

/**
 * Renders a single delivery route and its markers on the map.
 * @param {object} delivery - The delivery data object.
 */
async function renderSingleRoute(delivery) {
    const request = {
        origin: { lat: parseFloat(delivery.pickup_latitude), lng: parseFloat(delivery.pickup_longitude) },
        destination: { lat: parseFloat(delivery.delivery_latitude), lng: parseFloat(delivery.delivery_longitude) },
        travelMode: 'DRIVING'
    };

    try {
        const result = await directionsService.route(request);
        const routeConfig = getRouteStyle(delivery.status);

        const renderer = new google.maps.DirectionsRenderer({
            map: map,
            directions: result,
            suppressMarkers: true,
            preserveViewport: true,
            polylineOptions: {
                strokeColor: routeConfig.color,
                strokeWeight: routeConfig.weight,
                strokeOpacity: routeConfig.opacity,
                zIndex: routeConfig.zIndex,
            }
        });

        mapElements.routes.push({ bookingId: delivery.id, renderer, ...delivery });

        // Add custom markers for pickup and delivery
        addCustomMarker(delivery, 'pickup');
        addCustomMarker(delivery, 'delivery');

    } catch (error) {
        console.error(`❌ Failed to draw route for booking ${delivery.booking_id}:`, error);
    }
}

/**
 * Adds a custom styled marker to the map.
 * @param {object} delivery - The delivery data object.
 * @param {string} type - 'pickup' or 'delivery'.
 */
function addCustomMarker(delivery, type) {
    const isPickup = type === 'pickup';
    const position = {
        lat: parseFloat(isPickup ? delivery.pickup_latitude : delivery.delivery_latitude),
        lng: parseFloat(isPickup ? delivery.pickup_longitude : delivery.delivery_longitude),
    };
    const style = getMarkerStyle(type);

    const markerIcon = {
        path: style.path,
        fillColor: style.color,
        fillOpacity: 1,
        strokeWeight: 2,
        strokeColor: '#ffffff',
        rotation: 0,
        scale: 1.2,
        anchor: new google.maps.Point(12, 24),
    };

    const marker = new google.maps.Marker({
        position,
        map,
        title: isPickup ? `Pickup: ${delivery.pickup_address}` : `Delivery: ${delivery.delivery_address}`,
        icon: markerIcon,
        zIndex: 1000 // Ensure markers are above routes
    });

    const infoWindow = new google.maps.InfoWindow({
        content: buildInfoWindowContent(delivery, type),
        ariaLabel: delivery.booking_id,
    });

    marker.addListener('click', () => {
        closeAllInfoWindows();
        infoWindow.open(map, marker);
        activeInfoWindows.push(infoWindow);
    });

    mapElements.markers.push(marker);
}

// --- UI & EVENT HANDLING ---

/**
 * Sets up all event listeners for the UI controls.
 */
function setupEventListeners() {
    document.getElementById('searchInput').addEventListener('input', (e) => filterDeliveryList(e.target.value));
    document.getElementById('statusFilter').addEventListener('change', (e) => {
        const url = new URL(window.location);
        url.searchParams.set('status', e.target.value);
        window.location.href = url.toString(); // Reload page to get filtered server data
    });
    document.getElementById('autoRefresh').addEventListener('change', (e) => {
        if (e.target.checked) startAutoRefresh();
        else stopAutoRefresh();
    });
    document.getElementById('bookingModal').addEventListener('click', (e) => {
        if (e.target.id === 'bookingModal') closeBookingModal();
    });
}

/**
 * Filters the visible items in the delivery list on the right.
 * @param {string} searchTerm - The text to filter by.
 */
function filterDeliveryList(searchTerm) {
    const lowerCaseSearchTerm = searchTerm.toLowerCase();
    document.querySelectorAll('.delivery-item').forEach(item => {
        const itemText = item.textContent.toLowerCase();
        item.style.display = itemText.includes(lowerCaseSearchTerm) ? '' : 'none';
    });
}

/**
 * Pans and zooms the map to focus on a specific delivery route.
 * @param {number} bookingId - The ID of the booking to focus on.
 */
function focusOnDelivery(bookingId) {
    const routeData = mapElements.routes.find(r => r.bookingId === bookingId);
    if (routeData && routeData.renderer) {
        const bounds = routeData.renderer.getDirections().routes[0].bounds;
        map.fitBounds(bounds);
        // Highlight the list item
        document.querySelectorAll('.delivery-item').forEach(item => {
            item.classList.remove('bg-orange-100', 'ring-2', 'ring-orange-300');
            if (parseInt(item.dataset.bookingId) === bookingId) {
                item.classList.add('bg-orange-100', 'ring-2', 'ring-orange-300');
                item.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        });
    }
}

/**
 * Fetches and displays detailed booking information in a modal.
 * @param {number} bookingId - The ID of the booking to view.
 */
async function viewBookingDetails(bookingId) {
    const modal = document.getElementById('bookingModal');
    const modalContent = document.getElementById('modalContent');
    const modalTitle = document.getElementById('modalTitle');
    
    modal.classList.remove('hidden');
    modalContent.innerHTML = `<div class="text-center py-8"><i class="fas fa-spinner fa-spin text-3xl text-orange-500"></i></div>`;

    try {
        const response = await fetch(`/api/bookings/${bookingId}`);
        if (!response.ok) throw new Error('Failed to fetch details.');
        const data = await response.json();

        modalTitle.textContent = `Details for Booking #${data.booking_id}`;
        modalContent.innerHTML = buildModalContent(data);
    } catch (error) {
        modalContent.innerHTML = `<p class="text-red-500 text-center">${error.message}</p>`;
    }
}

/**
 * Closes the booking details modal.
 */
function closeBookingModal() {
    const modal = document.getElementById('bookingModal');
    modal.classList.add('hidden');
}

// --- UTILITY & HELPER FUNCTIONS ---

/**
 * Clears all markers and routes from the map.
 */
function clearMapElements() {
    mapElements.routes.forEach(route => route.renderer.setMap(null));
    mapElements.markers.forEach(marker => marker.setMap(null));
    mapElements.routes = [];
    mapElements.markers = [];
    closeAllInfoWindows();
}

/**
 * Fits the map viewport to show all currently rendered routes.
 */
function fitMapToAllRoutes() {
    if (mapElements.routes.length === 0) return;
    const bounds = new google.maps.LatLngBounds();
    mapElements.routes.forEach(route => {
        const routeBounds = route.renderer.getDirections().routes[0].bounds;
        bounds.union(routeBounds);
    });
    map.fitBounds(bounds);
}

/**
 * Returns style properties for a route based on its status.
 * @param {string} status - The delivery status.
 * @returns {object} Style object { color, weight, opacity, zIndex }.
 */
function getRouteStyle(status) {
    const styles = {
        'pending': { color: '#9CA3AF', weight: 3, opacity: 0.7, zIndex: 1 },
        'confirmed': { color: '#3B82F6', weight: 4, opacity: 0.8, zIndex: 2 },
        'in_progress': { color: '#8B5CF6', weight: 6, opacity: 0.9, zIndex: 4 },
        'delivery_enroute': { color: '#F59E0B', weight: 6, opacity: 0.9, zIndex: 3 },
        'completed': { color: '#10B981', weight: 3, opacity: 0.6, zIndex: 0 },
        'cancelled': { color: '#EF4444', weight: 3, opacity: 0.5, zIndex: 0 }
    };
    return styles[status] || styles['pending'];
}

/**
 * Returns style properties for a marker based on its type.
 * @param {string} type - 'pickup' or 'delivery'.
 * @returns {object} Style object { color, path }.
 */
function getMarkerStyle(type) {
    const styles = {
        'pickup': { color: '#10B981', path: google.maps.SymbolPath.CIRCLE },
        'delivery': { color: '#EF4444', path: google.maps.SymbolPath.CIRCLE }
    };
    return styles[type];
}

/**
 * Builds the HTML content for a marker's InfoWindow.
 * @param {object} delivery - The delivery data.
 * @param {string} type - 'pickup' or 'delivery'.
 * @returns {string} HTML content string.
 */
function buildInfoWindowContent(delivery, type) {
    const isPickup = type === 'pickup';
    const title = isPickup ? 'Pickup Location' : 'Delivery Location';
    const address = isPickup ? delivery.pickup_address : delivery.delivery_address;
    const color = isPickup ? 'green' : 'red';

    return `
        <div class="p-4">
            <h4 class="font-bold text-lg text-${color}-600">${title}</h4>
            <p class="text-gray-700 mt-1">${address}</p>
            <p class="text-sm text-gray-500 mt-2">Booking: <span class="font-semibold text-gray-800">${delivery.booking_id}</span></p>
            <button onclick="focusOnDelivery(${delivery.id})" class="mt-3 text-sm text-orange-600 hover:underline">Focus on this route</button>
        </div>
    `;
}

/**
 * Builds the HTML content for the details modal.
 * @param {object} data - The detailed booking data.
 * @returns {string} HTML content string.
 */
function buildModalContent(data) {
    // ... (This function can be expanded with more details if needed)
    return `
        <div class="space-y-4">
            <div>
                <h4 class="font-semibold text-gray-500 text-sm">Pickup</h4>
                <p class="text-gray-800">${data.pickup_address}</p>
                <p class="text-gray-600 text-sm">${data.pickup_person_name} (${data.pickup_person_phone})</p>
            </div>
            <div>
                <h4 class="font-semibold text-gray-500 text-sm">Delivery</h4>
                <p class="text-gray-800">${data.delivery_address}</p>
                <p class="text-gray-600 text-sm">${data.receiver_name} (${data.receiver_phone})</p>
            </div>
            <div class="grid grid-cols-3 gap-4 text-center pt-4 border-t">
                 <div>
                    <p class="text-xs text-gray-500">Status</p>
                    <p class="font-medium text-lg">${data.status.replace('_', ' ')}</p>
                </div>
                <div>
                    <p class="text-xs text-gray-500">Cost</p>
                    <p class="font-medium text-lg">${data.formatted_cost}</p>
                </div>
                <div>
                    <p class="text-xs text-gray-500">Distance</p>
                    <p class="font-medium text-lg">${data.distance_km} km</p>
                </div>
            </div>
        </div>
    `;
}

/**
 * Updates the map status indicator text and style.
 * @param {string} status - 'loading', 'ready', or 'error'.
 * @param {string} message - The text to display.
 */
function updateMapStatus(status, message) {
    const statusEl = document.getElementById('map-status');
    if (!statusEl) return;
    
    const icons = {
        loading: 'fas fa-spinner fa-spin',
        ready: 'fas fa-check-circle',
        error: 'fas fa-exclamation-triangle'
    };
    const colors = {
        loading: 'text-blue-600',
        ready: 'text-green-600',
        error: 'text-red-600'
    };

    statusEl.innerHTML = `<i class="${icons[status]} mr-2"></i>${message}`;
    statusEl.className = `absolute top-4 left-4 bg-white/80 backdrop-blur-sm px-3 py-1 rounded-full shadow-md text-xs font-medium z-10 transition-all ${colors[status]}`;
}

function closeAllInfoWindows() {
    activeInfoWindows.forEach(iw => iw.close());
    activeInfoWindows = [];
}

function refreshMap() {
    fetchAndRenderData();
}

function startAutoRefresh() {
    stopAutoRefresh(); // Ensure no multiple timers are running
    autoRefreshTimer = setInterval(fetchAndRenderData, 30000); // Refresh every 30 seconds
    console.log('✅ Auto-refresh started.');
}

function stopAutoRefresh() {
    clearInterval(autoRefreshTimer);
    console.log('🛑 Auto-refresh stopped.');
}

</script>
@endpush
